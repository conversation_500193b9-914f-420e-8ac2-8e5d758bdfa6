package io.gigsta.data.network

import io.gigsta.data.network.SupabaseClient
import io.github.jan.supabase.auth.auth
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

@Serializable
data class EmailApplicationResult(
    val subject: String,
    val body: String
)

@Serializable
data class EmailGenerationResponse(
    val success: Boolean,
    val emailApplication: EmailApplicationResult? = null,
    val emailId: String? = null,
    val error: String? = null
)

class ApiService {
    private val supabase = SupabaseClient.client
    private val httpClient = HttpClient()
    private val json = Json { ignoreUnknownKeys = true }
    
    // TODO: This should be configurable based on environment
    private val baseUrl = "http://********:3000" // For Android emulator
    // private val baseUrl = "http://localhost:3000" // For iOS simulator
    // private val baseUrl = "https://your-production-domain.com" // For production
    
    suspend fun generateEmailApplication(
        jobDescription: String? = null,
        jobImage: ByteArray? = null,
        unauthenticatedResumeFile: ByteArray? = null,
        unauthenticatedResumeFileName: String? = null
    ): Result<EmailGenerationResponse> {
        return try {
            // Get current user session for authentication
            val session = supabase.auth.currentSessionOrNull()
            if (session == null) {
                return Result.failure(Exception("User not authenticated"))
            }
            
            val response = httpClient.submitFormWithBinaryData(
                url = "$baseUrl/api/generate-email-application",
                formData = formData {
                    // Add job description if provided
                    jobDescription?.let { 
                        append("jobDescription", it)
                    }
                    
                    // Add job image if provided
                    jobImage?.let { imageBytes ->
                        append("jobImage", imageBytes, Headers.build {
                            append(HttpHeaders.ContentType, "image/jpeg")
                            append(HttpHeaders.ContentDisposition, "filename=\"job_image.jpg\"")
                        })
                    }
                    
                    // Add resume file if provided
                    if (unauthenticatedResumeFile != null && unauthenticatedResumeFileName != null) {
                        append("unauthenticatedResumeFile", unauthenticatedResumeFile, Headers.build {
                            append(HttpHeaders.ContentType, getMimeType(unauthenticatedResumeFileName))
                            append(HttpHeaders.ContentDisposition, "filename=\"$unauthenticatedResumeFileName\"")
                        })
                        append("unauthenticatedResumeFileName", unauthenticatedResumeFileName)
                    }
                }
            ) {
                // Add authorization header
                header("Authorization", "Bearer ${session.accessToken}")
            }
            
            val responseText = response.bodyAsText()
            
            if (response.status.isSuccess()) {
                val emailResponse = json.decodeFromString<EmailGenerationResponse>(responseText)
                Result.success(emailResponse)
            } else {
                val errorResponse = try {
                    json.decodeFromString<EmailGenerationResponse>(responseText)
                } catch (e: Exception) {
                    EmailGenerationResponse(
                        success = false,
                        error = "HTTP ${response.status.value}: ${response.status.description}"
                    )
                }
                Result.failure(Exception(errorResponse.error ?: "Unknown error"))
            }
            
        } catch (e: Exception) {
            Result.failure(Exception("Failed to generate email: ${e.message}"))
        }
    }
    
    suspend fun updateEmail(
        emailId: String,
        subject: String,
        body: String
    ): Result<Boolean> {
        return try {
            // Get current user session for authentication
            val session = supabase.auth.currentSessionOrNull()
            if (session == null) {
                return Result.failure(Exception("User not authenticated"))
            }
            
            val response = httpClient.submitFormWithBinaryData(
                url = "$baseUrl/api/update-email",
                formData = formData {
                    append("emailId", emailId)
                    append("subject", subject)
                    append("body", body)
                }
            ) {
                header("Authorization", "Bearer ${session.accessToken}")
            }
            
            val responseText = response.bodyAsText()
            
            if (response.status.isSuccess()) {
                val updateResponse = json.decodeFromString<Map<String, Any>>(responseText)
                val success = updateResponse["success"] as? Boolean ?: false
                if (success) {
                    Result.success(true)
                } else {
                    val error = updateResponse["error"] as? String ?: "Unknown error"
                    Result.failure(Exception(error))
                }
            } else {
                Result.failure(Exception("HTTP ${response.status.value}: ${response.status.description}"))
            }
            
        } catch (e: Exception) {
            Result.failure(Exception("Failed to update email: ${e.message}"))
        }
    }
    
    private fun getMimeType(fileName: String): String {
        return when {
            fileName.endsWith(".pdf", ignoreCase = true) -> "application/pdf"
            fileName.endsWith(".docx", ignoreCase = true) -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            fileName.endsWith(".doc", ignoreCase = true) -> "application/msword"
            fileName.endsWith(".png", ignoreCase = true) -> "image/png"
            fileName.endsWith(".jpg", ignoreCase = true) || fileName.endsWith(".jpeg", ignoreCase = true) -> "image/jpeg"
            else -> "application/octet-stream"
        }
    }
}
