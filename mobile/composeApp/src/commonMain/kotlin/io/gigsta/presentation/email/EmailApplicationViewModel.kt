package io.gigsta.presentation.email

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.gigsta.domain.model.User
import io.gigsta.domain.repository.AuthRepository
import io.gigsta.data.network.ApiService
import kotlinx.coroutines.launch

enum class InputMethod {
    TEXT,
    IMAGE
}

data class ExistingResume(
    val fileName: String,
    val uploadedAt: String,
    val unauthenticatedResumeFile: String? = null
)

data class EmailApplicationUiState(
    val currentStep: Int = 1,
    val isResumeLoading: Boolean = false,
    val existingResume: ExistingResume? = null,
    val uploadSuccess: Boolean = false,
    val jobDescription: String = "",
    val jobImage: ByteArray? = null,
    val inputMethod: InputMethod = InputMethod.TEXT,
    val isGenerating: Boolean = false,
    val emailSubject: String = "",
    val emailBody: String = "",
    val isSubjectEditable: Boolean = false,
    val isBodyEditable: Boolean = false,
    val isSaving: Boolean = false,
    val currentEmailId: String? = null,
    val error: String? = null,
    val user: User? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as EmailApplicationUiState

        if (currentStep != other.currentStep) return false
        if (isResumeLoading != other.isResumeLoading) return false
        if (existingResume != other.existingResume) return false
        if (uploadSuccess != other.uploadSuccess) return false
        if (jobDescription != other.jobDescription) return false
        if (jobImage != null) {
            if (other.jobImage == null) return false
            if (!jobImage.contentEquals(other.jobImage)) return false
        } else if (other.jobImage != null) return false
        if (inputMethod != other.inputMethod) return false
        if (isGenerating != other.isGenerating) return false
        if (emailSubject != other.emailSubject) return false
        if (emailBody != other.emailBody) return false
        if (isSubjectEditable != other.isSubjectEditable) return false
        if (isBodyEditable != other.isBodyEditable) return false
        if (isSaving != other.isSaving) return false
        if (currentEmailId != other.currentEmailId) return false
        if (error != other.error) return false
        if (user != other.user) return false

        return true
    }

    override fun hashCode(): Int {
        var result = currentStep
        result = 31 * result + isResumeLoading.hashCode()
        result = 31 * result + (existingResume?.hashCode() ?: 0)
        result = 31 * result + uploadSuccess.hashCode()
        result = 31 * result + jobDescription.hashCode()
        result = 31 * result + (jobImage?.contentHashCode() ?: 0)
        result = 31 * result + inputMethod.hashCode()
        result = 31 * result + isGenerating.hashCode()
        result = 31 * result + emailSubject.hashCode()
        result = 31 * result + emailBody.hashCode()
        result = 31 * result + isSubjectEditable.hashCode()
        result = 31 * result + isBodyEditable.hashCode()
        result = 31 * result + isSaving.hashCode()
        result = 31 * result + (currentEmailId?.hashCode() ?: 0)
        result = 31 * result + (error?.hashCode() ?: 0)
        result = 31 * result + (user?.hashCode() ?: 0)
        return result
    }
}

class EmailApplicationViewModel(
    private val authRepository: AuthRepository,
    private val apiService: ApiService = ApiService()
) : ViewModel() {
    
    var uiState by mutableStateOf(EmailApplicationUiState())
        private set
    
    init {
        loadCurrentUser()
    }
    
    private fun loadCurrentUser() {
        viewModelScope.launch {
            try {
                val user = authRepository.getCurrentUser()
                uiState = uiState.copy(user = user)
            } catch (e: Exception) {
                uiState = uiState.copy(error = "Failed to load user: ${e.message}")
            }
        }
    }
    
    fun goToNextStep() {
        if (validateCurrentStep()) {
            uiState = uiState.copy(
                currentStep = (uiState.currentStep + 1).coerceAtMost(3),
                error = null
            )
        }
    }
    
    fun goToPreviousStep() {
        uiState = uiState.copy(
            currentStep = (uiState.currentStep - 1).coerceAtLeast(1),
            error = null
        )
    }
    
    private fun validateCurrentStep(): Boolean {
        return when (uiState.currentStep) {
            1 -> {
                if (!uiState.uploadSuccess || uiState.existingResume == null) {
                    uiState = uiState.copy(error = "Harap unggah resume terlebih dahulu")
                    false
                } else {
                    true
                }
            }
            2 -> {
                val hasJobDescription = uiState.inputMethod == InputMethod.TEXT && uiState.jobDescription.isNotBlank()
                val hasJobImage = uiState.inputMethod == InputMethod.IMAGE && uiState.jobImage != null
                
                if (!hasJobDescription && !hasJobImage) {
                    uiState = uiState.copy(
                        error = if (uiState.inputMethod == InputMethod.TEXT) {
                            "Harap isi informasi lowongan"
                        } else {
                            "Harap unggah poster lowongan"
                        }
                    )
                    false
                } else {
                    true
                }
            }
            else -> true
        }
    }
    
    fun onResumeUpload(file: ByteArray, fileName: String) {
        // TODO: Implement resume upload
        uiState = uiState.copy(
            isResumeLoading = true,
            error = null
        )
        
        viewModelScope.launch {
            try {
                // Simulate upload
                kotlinx.coroutines.delay(2000)
                
                uiState = uiState.copy(
                    isResumeLoading = false,
                    uploadSuccess = true,
                    existingResume = ExistingResume(
                        fileName = fileName,
                        uploadedAt = "2024-01-01T00:00:00Z"
                    )
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isResumeLoading = false,
                    error = "Failed to upload resume: ${e.message}"
                )
            }
        }
    }
    
    fun onViewResume() {
        // TODO: Implement view resume
    }
    
    fun onDeleteResume() {
        uiState = uiState.copy(
            existingResume = null,
            uploadSuccess = false
        )
    }
    
    fun onJobDescriptionChange(description: String) {
        uiState = uiState.copy(jobDescription = description)
    }
    
    fun onJobImageChange(image: ByteArray?) {
        uiState = uiState.copy(jobImage = image)
    }
    
    fun onInputMethodChange(method: InputMethod) {
        uiState = uiState.copy(inputMethod = method)
    }
    
    fun onEmailSubjectChange(subject: String) {
        uiState = uiState.copy(emailSubject = subject)
    }
    
    fun onEmailBodyChange(body: String) {
        uiState = uiState.copy(emailBody = body)
    }
    
    fun onToggleSubjectEdit() {
        uiState = uiState.copy(isSubjectEditable = !uiState.isSubjectEditable)
    }
    
    fun onToggleBodyEdit() {
        uiState = uiState.copy(isBodyEditable = !uiState.isBodyEditable)
    }
    
    fun onSaveEmailChanges() {
        val emailId = uiState.currentEmailId
        if (emailId == null) {
            uiState = uiState.copy(error = "No email to save")
            return
        }

        uiState = uiState.copy(isSaving = true)

        viewModelScope.launch {
            try {
                val result = apiService.updateEmail(
                    emailId = emailId,
                    subject = uiState.emailSubject,
                    body = uiState.emailBody
                )

                result.fold(
                    onSuccess = {
                        uiState = uiState.copy(
                            isSaving = false,
                            isSubjectEditable = false,
                            isBodyEditable = false
                        )
                    },
                    onFailure = { error ->
                        uiState = uiState.copy(
                            isSaving = false,
                            error = "Failed to save changes: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isSaving = false,
                    error = "Failed to save changes: ${e.message}"
                )
            }
        }
    }
    
    fun onGenerateEmail() {
        if (uiState.user == null) {
            uiState = uiState.copy(error = "Please log in to generate email")
            return
        }

        uiState = uiState.copy(
            isGenerating = true,
            error = null,
            emailSubject = "",
            emailBody = "",
            isSubjectEditable = false,
            isBodyEditable = false
        )

        viewModelScope.launch {
            try {
                val result = apiService.generateEmailApplication(
                    jobDescription = if (uiState.inputMethod == InputMethod.TEXT) uiState.jobDescription else null,
                    jobImage = if (uiState.inputMethod == InputMethod.IMAGE) uiState.jobImage else null,
                    unauthenticatedResumeFile = uiState.existingResume?.unauthenticatedResumeFile?.let {
                        // Convert base64 string back to ByteArray if needed
                        // For now, we'll use dummy data since we don't have actual file upload
                        ByteArray(0)
                    },
                    unauthenticatedResumeFileName = uiState.existingResume?.fileName
                )

                result.fold(
                    onSuccess = { response ->
                        if (response.success && response.emailApplication != null) {
                            uiState = uiState.copy(
                                isGenerating = false,
                                emailSubject = response.emailApplication.subject,
                                emailBody = response.emailApplication.body,
                                currentEmailId = response.emailId
                            )
                        } else {
                            uiState = uiState.copy(
                                isGenerating = false,
                                error = response.error ?: "Failed to generate email"
                            )
                        }
                    },
                    onFailure = { error ->
                        uiState = uiState.copy(
                            isGenerating = false,
                            error = "Failed to generate email: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isGenerating = false,
                    error = "Failed to generate email: ${e.message}"
                )
            }
        }
    }
}
