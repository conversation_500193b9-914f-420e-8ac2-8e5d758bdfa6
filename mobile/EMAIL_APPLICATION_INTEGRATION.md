# Email Application Feature Integration

This document describes the integration of the email application generator functionality from the web application to the mobile app.

## Overview

The mobile app now includes a complete email application generator that replicates the functionality from the web application (`/web/app/email-application/page.tsx`). Users can:

1. Upload their CV/Resume
2. Input job information (text description or job poster image)
3. Generate personalized email applications using AI
4. Edit and save the generated emails

## Architecture

### Navigation
- **App.kt**: Updated with `AppScreen` enum and navigation state management
- **HomeScreen.kt**: Updated to accept navigation callbacks
- **HomeViewModel.kt**: Updated to handle navigation to email application screen

### Email Application Screen
- **EmailApplicationScreen.kt**: Main screen composable with step-based navigation
- **EmailApplicationViewModel.kt**: Manages state, form data, and API calls
- **Components**:
  - `StepIndicator.kt`: Visual step progress indicator
  - `ResumeUploadStep.kt`: Resume upload functionality
  - `JobInfoStep.kt`: Job information input (text or image)
  - `EmailResultStep.kt`: Display and edit generated emails

### API Integration
- **ApiService.kt**: HTTP client for communicating with web API endpoints
- Integrates with existing `/api/generate-email-application` endpoint
- Handles authentication using Supabase session tokens
- Supports both job description text and job poster image uploads

### Data Models
- **InputMethod**: Enum for text vs image input methods
- **ExistingResume**: Model for uploaded resume data
- **EmailApplicationUiState**: Complete UI state management
- **EmailApplicationResult**: API response model
- **EmailGenerationResponse**: API response wrapper

## Features

### Step 1: Resume Upload
- File picker integration (simulated for now)
- Support for PDF, DOC, DOCX formats
- File validation and error handling
- Resume preview and management

### Step 2: Job Information
- Toggle between text description and image upload
- Rich text input for job descriptions
- Image upload for job posters
- Input validation and error handling

### Step 3: Email Generation & Results
- AI-powered email generation
- Real-time generation progress
- Editable email subject and body
- Copy to clipboard functionality
- Save changes to backend

## API Endpoints Used

### Generate Email Application
- **Endpoint**: `POST /api/generate-email-application`
- **Authentication**: Bearer token from Supabase session
- **Parameters**:
  - `jobDescription` (optional): Text description of the job
  - `jobImage` (optional): Job poster image file
  - `unauthenticatedResumeFile` (optional): Resume file data
  - `unauthenticatedResumeFileName` (optional): Resume file name

### Update Email
- **Endpoint**: `POST /api/update-email`
- **Authentication**: Bearer token from Supabase session
- **Parameters**:
  - `emailId`: ID of the email to update
  - `subject`: Updated email subject
  - `body`: Updated email body

## Configuration

### Network Configuration
Update `NetworkConfig.kt` with the correct API base URL:

```kotlin
// For development
private val baseUrl = "http://********:3000" // Android emulator
// private val baseUrl = "http://localhost:3000" // iOS simulator
// private val baseUrl = "https://your-domain.com" // Production
```

### Dependencies
The following dependencies are already included in `build.gradle.kts`:
- Ktor HTTP client for API calls
- Kotlinx Serialization for JSON handling
- Supabase Auth for authentication

## Usage Flow

1. User navigates to "Email Lamaran" tab in the home screen
2. Clicks "Tambah Email Lamaran" button
3. Navigates to EmailApplicationScreen
4. Follows 3-step process:
   - Upload resume
   - Input job information
   - Generate and edit email
5. Can save changes and return to home screen

## Error Handling

- Network connectivity issues
- Authentication failures
- File upload errors
- API response errors
- Form validation errors

## Future Enhancements

1. **File Picker Integration**: Implement actual file picker for resume and image uploads
2. **Offline Support**: Cache generated emails for offline viewing
3. **Email Templates**: Add predefined email templates
4. **History Integration**: Better integration with email history from HomeScreen
5. **Push Notifications**: Notify users when email generation is complete

## Testing

To test the email application feature:

1. Ensure the web API server is running
2. Update `NetworkConfig.kt` with correct API URL
3. Run the mobile app
4. Navigate to "Email Lamaran" tab
5. Click "Tambah Email Lamaran"
6. Follow the 3-step process

## Code Quality

The implementation follows clean architecture principles:
- Separation of concerns between UI, ViewModel, and API layers
- Proper error handling and loading states
- Consistent naming conventions
- Comprehensive state management
- Reusable UI components
